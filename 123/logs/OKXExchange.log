2025-07-30 14:19:42 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-07-30 14:19:42 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-07-30 14:19:42 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-30 14:19:42 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 14:19:42.198 [INFO] [exchanges.okx_exchange] 🔧 OKX API限制根源修复为2次/秒，确保30+代币健壮启动
2025-07-30 14:19:42.198 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=2次/秒
2025-07-30 14:19:42 [INFO] [OKXExchange] 🔥 开始初始化OKX账户配置...
2025-07-30 14:19:42 [INFO] [OKXExchange] OKX当前账户模式: 2
2025-07-30 14:19:42 [WARNING] [OKXExchange] OKX账户不是跨币种保证金模式，当前模式: 2
2025-07-30 14:19:47 [INFO] [OKXExchange] OKX设置为单向持仓模式
2025-07-30 14:19:47.323 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SPK-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 14:19:52.343 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 14:19:52.343 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 14:19:57.333 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 14:19:57.333 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 14:19:57 [DEBUG] [OKXExchange] OKX预设置杠杆成功: SPK-USDT
2025-07-30 14:19:57.333 [INFO] [exchanges.okx_exchange] OKX设置杠杆: RESOLV-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 14:20:02.350 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 14:20:02.350 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 14:20:07.329 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 14:20:07.330 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 14:20:07 [DEBUG] [OKXExchange] OKX预设置杠杆成功: RESOLV-USDT
2025-07-30 14:20:07.330 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ICNT-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 14:20:12.340 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 14:20:12.340 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 14:20:17.330 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 14:20:17.330 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 14:20:17 [DEBUG] [OKXExchange] OKX预设置杠杆成功: ICNT-USDT
2025-07-30 14:20:17.330 [INFO] [exchanges.okx_exchange] OKX设置杠杆: CAKE-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 14:20:22.342 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 14:20:22.342 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 14:20:27.327 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 14:20:27.328 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 14:20:27 [DEBUG] [OKXExchange] OKX预设置杠杆成功: CAKE-USDT
2025-07-30 14:20:27 [INFO] [OKXExchange] ✅ OKX账户初始化完成
2025-07-30 14:20:32.402 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-07-30 14:20:37.324 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-07-30 14:20:42.330 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-30 14:20:47.348 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-30 14:20:52.342 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-30 15:16:42 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-07-30 15:16:42 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-07-30 15:16:42 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-30 15:16:42 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 15:16:42.182 [INFO] [exchanges.okx_exchange] 🔧 OKX API限制根源修复为2次/秒，确保30+代币健壮启动
2025-07-30 15:16:42.182 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=2次/秒
2025-07-30 15:16:42 [INFO] [OKXExchange] 🔥 开始初始化OKX账户配置...
2025-07-30 15:16:42 [INFO] [OKXExchange] OKX当前账户模式: 2
2025-07-30 15:16:42 [WARNING] [OKXExchange] OKX账户不是跨币种保证金模式，当前模式: 2
2025-07-30 15:16:47 [INFO] [OKXExchange] OKX设置为单向持仓模式
2025-07-30 15:16:47.326 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SPK-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 15:16:52.298 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 15:16:52.299 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 15:16:57.301 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 15:16:57.301 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 15:16:57 [DEBUG] [OKXExchange] OKX预设置杠杆成功: SPK-USDT
2025-07-30 15:16:57.302 [INFO] [exchanges.okx_exchange] OKX设置杠杆: RESOLV-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 15:17:02.303 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 15:17:02.303 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 15:17:07.319 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 15:17:07.319 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 15:17:07 [DEBUG] [OKXExchange] OKX预设置杠杆成功: RESOLV-USDT
2025-07-30 15:17:07.319 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ICNT-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 15:17:12.322 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 15:17:12.322 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 15:17:17.317 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 15:17:17.318 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 15:17:17 [DEBUG] [OKXExchange] OKX预设置杠杆成功: ICNT-USDT
2025-07-30 15:17:17.318 [INFO] [exchanges.okx_exchange] OKX设置杠杆: CAKE-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 15:17:22.302 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 15:17:22.302 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 15:17:27.353 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 15:17:27.353 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 15:17:27 [DEBUG] [OKXExchange] OKX预设置杠杆成功: CAKE-USDT
2025-07-30 15:17:27 [INFO] [OKXExchange] ✅ OKX账户初始化完成
2025-07-30 15:17:32.311 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-07-30 15:17:37.298 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-07-30 16:48:24 [ERROR] [OKXExchange] ❌ OKX统一模块初始化失败: could not convert string to float: '110.0        # 实盘最大订单金额($200)'
2025-07-30 16:49:02 [ERROR] [OKXExchange] ❌ OKX统一模块初始化失败: could not convert string to float: '250.0      # 单次交易限制($250)'
2025-07-30 16:50:45 [ERROR] [OKXExchange] ❌ OKX统一模块初始化失败: could not convert string to float: '250.0      # 单次交易限制($250)'
2025-07-30 16:55:13 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-07-30 16:55:13 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-07-30 16:55:13 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-30 16:55:13 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 16:55:13.042 [INFO] [exchanges.okx_exchange] 🔧 OKX API限制根源修复为2次/秒，确保30+代币健壮启动
2025-07-30 16:55:13.043 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=2次/秒
2025-07-30 16:55:13 [INFO] [OKXExchange] 🔥 开始初始化OKX账户配置...
2025-07-30 16:55:13 [INFO] [OKXExchange] OKX当前账户模式: 2
2025-07-30 16:55:13 [WARNING] [OKXExchange] OKX账户不是跨币种保证金模式，当前模式: 2
2025-07-30 16:55:18 [INFO] [OKXExchange] OKX设置为单向持仓模式
2025-07-30 16:55:18.223 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SPK-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 16:55:23.168 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 16:55:23.168 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 16:55:28.158 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 16:55:28.158 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 16:55:28 [DEBUG] [OKXExchange] OKX预设置杠杆成功: SPK-USDT
2025-07-30 16:55:28.158 [INFO] [exchanges.okx_exchange] OKX设置杠杆: RESOLV-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 16:55:33.172 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 16:55:33.172 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 16:55:38.185 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 16:55:38.186 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 16:55:38 [DEBUG] [OKXExchange] OKX预设置杠杆成功: RESOLV-USDT
2025-07-30 16:55:38.186 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ICNT-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 16:55:43.179 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 16:55:43.179 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 16:55:48.181 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 16:55:48.181 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 16:55:48 [DEBUG] [OKXExchange] OKX预设置杠杆成功: ICNT-USDT
2025-07-30 16:55:48.181 [INFO] [exchanges.okx_exchange] OKX设置杠杆: CAKE-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 16:55:53.186 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 16:55:53.186 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 16:55:58.168 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 16:55:58.169 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 16:55:58 [DEBUG] [OKXExchange] OKX预设置杠杆成功: CAKE-USDT
2025-07-30 16:55:58 [INFO] [OKXExchange] ✅ OKX账户初始化完成
2025-07-30 16:56:03.191 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-07-30 16:56:08.170 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-07-30 16:56:13.174 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-30 16:56:18.191 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-30 16:56:23.170 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-30 16:56:28.169 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-30 16:56:28.178 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 16:56:28.178 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 16:56:33.184 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-30 16:56:38.191 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-30 16:56:38.193 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 16:56:38.193 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 16:56:48.179 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 16:56:48.179 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 16:56:58.176 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 16:56:58.176 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 16:57:08.194 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 16:57:08.195 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 16:57:18.205 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 16:57:18.205 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 16:58:19.345 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-07-30 16:58:19.841 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: WIF-USDT -> 最大杠杆=20x
2025-07-30 16:58:19.842 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-30 16:58:19.844 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-07-30 16:58:19.856 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-30 16:58:26.930 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: AI16Z-USDT -> 最大杠杆=20x
2025-07-30 16:58:27.430 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOT-USDT -> 最大杠杆=50x
2025-07-30 16:58:27.431 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: JUP-USDT -> 最大杠杆=50x
2025-07-30 16:58:27.433 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SOL-USDT -> 最大杠杆=50x
2025-07-30 16:58:27.445 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: MATIC-USDT
2025-07-30 16:58:28.447 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SPK-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 16:58:28.947 [INFO] [exchanges.okx_exchange] OKX设置杠杆: RESOLV-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 16:58:28.948 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ICNT-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 16:58:28.948 [INFO] [exchanges.okx_exchange] OKX设置杠杆: CAKE-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 16:58:28.948 [INFO] [exchanges.okx_exchange] OKX设置杠杆: WIF-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 16:58:28.948 [INFO] [exchanges.okx_exchange] OKX设置杠杆: AI16Z-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 16:58:28.948 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SOL-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 16:58:28.949 [INFO] [exchanges.okx_exchange] OKX设置杠杆: MATIC-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 16:58:28.949 [INFO] [exchanges.okx_exchange] OKX设置杠杆: DOT-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 16:58:28.949 [INFO] [exchanges.okx_exchange] OKX设置杠杆: JUP-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 16:58:33.519 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 16:58:33.519 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 16:58:34.020 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 16:58:34.020 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 16:58:34.032 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 16:58:34.032 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 16:58:34.037 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 16:58:34.038 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 16:58:34.040 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 16:58:34.041 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 16:58:34.041 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-07-30 16:58:34.041 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-07-30 16:58:34.041 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-07-30 16:58:34.041 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-07-30 16:58:34.041 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-07-30 16:58:34.042 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-07-30 16:58:34.042 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-30 16:58:34.042 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-07-30 16:58:34.042 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-30 16:58:34.042 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 16:58:34.044 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-07-30 16:58:34.044 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-07-30 16:58:34.044 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-07-30 16:58:34.044 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-07-30 16:58:34.044 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-07-30 16:58:34.044 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-07-30 16:58:34.045 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-30 16:58:34.045 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-07-30 16:58:34.045 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-30 16:58:34.045 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 16:58:34.047 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-07-30 16:58:34.047 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-07-30 16:58:34.047 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-07-30 16:58:34.048 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-07-30 16:58:34.048 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-07-30 16:58:34.048 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-07-30 16:58:34.048 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-30 16:58:34.048 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-07-30 16:58:34.048 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-30 16:58:34.048 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 16:58:34.054 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-07-30 16:58:34.054 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-07-30 16:58:34.054 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-07-30 16:58:34.054 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-07-30 16:58:34.055 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-07-30 16:58:34.055 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-07-30 16:58:34.055 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-30 16:58:34.055 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-07-30 16:58:34.055 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-30 16:58:34.055 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 16:58:34.056 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-07-30 16:58:34.056 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-07-30 16:58:34.056 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-07-30 16:58:34.056 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-07-30 16:58:34.056 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-07-30 16:58:34.056 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-07-30 16:58:34.056 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-30 16:58:34.056 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-07-30 16:58:34.056 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-30 16:58:34.057 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 16:58:38.608 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 16:58:38.608 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 16:58:39.018 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 16:58:39.019 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 16:58:39.031 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 16:58:39.031 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 16:58:39.032 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 16:58:39.032 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 16:58:39.036 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 16:58:39.036 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 16:58:39.038 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 16:58:39.038 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 16:58:39.039 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 16:58:39.039 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 16:58:39.049 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 16:58:39.049 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 16:58:39.054 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 16:58:39.055 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 16:58:39.085 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 16:58:39.085 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 16:58:49.874 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-07-30 16:58:54.801 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-07-30 16:58:59.767 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-30 16:59:04.765 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-30 16:59:09.767 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-30 16:59:14.774 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-30 16:59:19.772 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-30 16:59:24.772 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-30 16:59:29.770 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-07-30 16:59:34.795 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-07-30 16:59:39.780 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-30 16:59:44.781 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-30 16:59:49.784 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-30 16:59:54.825 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-30 16:59:59.816 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-30 17:00:04.819 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-30 17:04:37 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-07-30 17:04:37 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-07-30 17:04:37 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-30 17:04:37 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 17:04:37.962 [INFO] [exchanges.okx_exchange] 🔧 OKX API限制根源修复为2次/秒，确保30+代币健壮启动
2025-07-30 17:04:37.962 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=2次/秒
2025-07-30 17:04:37 [INFO] [OKXExchange] 🔥 开始初始化OKX账户配置...
2025-07-30 17:04:38 [INFO] [OKXExchange] OKX当前账户模式: 2
2025-07-30 17:04:38 [WARNING] [OKXExchange] OKX账户不是跨币种保证金模式，当前模式: 2
2025-07-30 17:04:43 [INFO] [OKXExchange] OKX设置为单向持仓模式
2025-07-30 17:04:43.089 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SPK-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:04:48.092 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:04:48.092 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:04:53.083 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:04:53.084 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:04:53 [DEBUG] [OKXExchange] OKX预设置杠杆成功: SPK-USDT
2025-07-30 17:04:53.084 [INFO] [exchanges.okx_exchange] OKX设置杠杆: RESOLV-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:04:58.085 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:04:58.085 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:05:03.086 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:05:03.086 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:05:03 [DEBUG] [OKXExchange] OKX预设置杠杆成功: RESOLV-USDT
2025-07-30 17:05:03.086 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ICNT-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:06:34 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-07-30 17:06:34 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-07-30 17:06:34 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-30 17:06:34 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 17:06:34.906 [INFO] [exchanges.okx_exchange] 🔧 OKX API限制根源修复为2次/秒，确保30+代币健壮启动
2025-07-30 17:06:34.907 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=2次/秒
2025-07-30 17:06:34 [INFO] [OKXExchange] 🔥 开始初始化OKX账户配置...
2025-07-30 17:06:35 [INFO] [OKXExchange] OKX当前账户模式: 2
2025-07-30 17:06:35 [WARNING] [OKXExchange] OKX账户不是跨币种保证金模式，当前模式: 2
2025-07-30 17:06:35 [INFO] [OKXExchange] OKX设置为单向持仓模式
2025-07-30 17:06:35.544 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SPK-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:06:36.043 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:06:36.044 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:06:36.537 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:06:36.537 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:06:36 [DEBUG] [OKXExchange] OKX预设置杠杆成功: SPK-USDT
2025-07-30 17:06:36.537 [INFO] [exchanges.okx_exchange] OKX设置杠杆: RESOLV-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:06:37.058 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:06:37.058 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:06:37.531 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:06:37.531 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:06:37 [DEBUG] [OKXExchange] OKX预设置杠杆成功: RESOLV-USDT
2025-07-30 17:06:37.531 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ICNT-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:06:38.042 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:06:38.043 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:06:38.535 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:06:38.536 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:06:38 [DEBUG] [OKXExchange] OKX预设置杠杆成功: ICNT-USDT
2025-07-30 17:06:38.536 [INFO] [exchanges.okx_exchange] OKX设置杠杆: CAKE-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:06:39.044 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:06:39.044 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:06:39.538 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:06:39.538 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:06:39 [DEBUG] [OKXExchange] OKX预设置杠杆成功: CAKE-USDT
2025-07-30 17:06:39 [INFO] [OKXExchange] ✅ OKX账户初始化完成
2025-07-30 17:06:40.038 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-07-30 17:06:41.249 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-07-30 17:06:42.754 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-30 17:06:43.760 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-30 17:06:44.356 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-30 17:06:45.892 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-30 17:06:46.479 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-30 17:06:47.097 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-30 17:08:09 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-07-30 17:08:09 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-07-30 17:08:09 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-30 17:08:09 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 17:08:09.444 [INFO] [exchanges.okx_exchange] 🔧 OKX API限制根源修复为2次/秒，确保30+代币健壮启动
2025-07-30 17:08:09.445 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=2次/秒
2025-07-30 17:08:09 [INFO] [OKXExchange] 🔥 开始初始化OKX账户配置...
2025-07-30 17:08:09 [INFO] [OKXExchange] OKX当前账户模式: 2
2025-07-30 17:08:09 [WARNING] [OKXExchange] OKX账户不是跨币种保证金模式，当前模式: 2
2025-07-30 17:08:10 [INFO] [OKXExchange] OKX设置为单向持仓模式
2025-07-30 17:08:10.064 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SPK-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:08:10.574 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:08:10.574 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:08:11.064 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:08:11.064 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:08:11 [DEBUG] [OKXExchange] OKX预设置杠杆成功: SPK-USDT
2025-07-30 17:08:11.065 [INFO] [exchanges.okx_exchange] OKX设置杠杆: RESOLV-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:08:11.564 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:08:11.564 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:08:12.071 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:08:12.071 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:08:12 [DEBUG] [OKXExchange] OKX预设置杠杆成功: RESOLV-USDT
2025-07-30 17:08:12.071 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ICNT-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:08:12.567 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:08:12.567 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:08:13.068 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:08:13.068 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:08:13 [DEBUG] [OKXExchange] OKX预设置杠杆成功: ICNT-USDT
2025-07-30 17:08:13.068 [INFO] [exchanges.okx_exchange] OKX设置杠杆: CAKE-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:08:13.582 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:08:13.582 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:08:14.082 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:08:14.082 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:08:14 [DEBUG] [OKXExchange] OKX预设置杠杆成功: CAKE-USDT
2025-07-30 17:08:14 [INFO] [OKXExchange] ✅ OKX账户初始化完成
2025-07-30 17:09:10 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-07-30 17:09:10 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-07-30 17:09:10 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-30 17:09:10 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 17:09:10.960 [INFO] [exchanges.okx_exchange] 🔧 OKX API限制根源修复为2次/秒，确保30+代币健壮启动
2025-07-30 17:09:10.960 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=2次/秒
2025-07-30 17:09:10 [INFO] [OKXExchange] 🔥 开始初始化OKX账户配置...
2025-07-30 17:09:11 [INFO] [OKXExchange] OKX当前账户模式: 2
2025-07-30 17:09:11 [WARNING] [OKXExchange] OKX账户不是跨币种保证金模式，当前模式: 2
2025-07-30 17:09:11 [INFO] [OKXExchange] OKX设置为单向持仓模式
2025-07-30 17:09:11.624 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SPK-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:09:12.080 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:09:12.081 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:09:12.592 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:09:12.592 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:09:12 [DEBUG] [OKXExchange] OKX预设置杠杆成功: SPK-USDT
2025-07-30 17:09:12.592 [INFO] [exchanges.okx_exchange] OKX设置杠杆: RESOLV-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:09:13.079 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:09:13.080 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:09:13.582 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:09:13.583 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:09:13 [DEBUG] [OKXExchange] OKX预设置杠杆成功: RESOLV-USDT
2025-07-30 17:09:13.583 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ICNT-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:09:14.070 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:09:14.070 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:09:14.570 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:09:14.570 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:09:14 [DEBUG] [OKXExchange] OKX预设置杠杆成功: ICNT-USDT
2025-07-30 17:09:14.570 [INFO] [exchanges.okx_exchange] OKX设置杠杆: CAKE-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:09:15.080 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:09:15.080 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:09:15.581 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:09:15.581 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:09:15 [DEBUG] [OKXExchange] OKX预设置杠杆成功: CAKE-USDT
2025-07-30 17:09:15 [INFO] [OKXExchange] ✅ OKX账户初始化完成
2025-07-30 17:16:57 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-07-30 17:16:57 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-07-30 17:16:57 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-30 17:16:57 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 17:16:57.351 [INFO] [exchanges.okx_exchange] 🔧 OKX API限制根源修复为2次/秒，确保30+代币健壮启动
2025-07-30 17:16:57.351 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=2次/秒
2025-07-30 17:16:57 [INFO] [OKXExchange] 🔥 开始初始化OKX账户配置...
2025-07-30 17:16:57 [INFO] [OKXExchange] OKX当前账户模式: 2
2025-07-30 17:16:57 [WARNING] [OKXExchange] OKX账户不是跨币种保证金模式，当前模式: 2
2025-07-30 17:16:57 [INFO] [OKXExchange] OKX设置为单向持仓模式
2025-07-30 17:16:57.969 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SPK-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:16:58.466 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:16:58.466 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:16:58.969 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:16:58.969 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:16:58 [DEBUG] [OKXExchange] OKX预设置杠杆成功: SPK-USDT
2025-07-30 17:16:58.969 [INFO] [exchanges.okx_exchange] OKX设置杠杆: RESOLV-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:16:59.465 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:16:59.465 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:16:59.964 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:16:59.964 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:16:59 [DEBUG] [OKXExchange] OKX预设置杠杆成功: RESOLV-USDT
2025-07-30 17:16:59.964 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ICNT-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:17:00.463 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:17:00.463 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:17:00.973 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:17:00.973 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:17:00 [DEBUG] [OKXExchange] OKX预设置杠杆成功: ICNT-USDT
2025-07-30 17:17:00.973 [INFO] [exchanges.okx_exchange] OKX设置杠杆: CAKE-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:17:01.463 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:17:01.463 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:17:01.976 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:17:01.976 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:17:01 [DEBUG] [OKXExchange] OKX预设置杠杆成功: CAKE-USDT
2025-07-30 17:17:01 [INFO] [OKXExchange] ✅ OKX账户初始化完成
2025-07-30 17:17:02.467 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-07-30 17:17:03.705 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-07-30 17:17:05.203 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-30 17:17:06.210 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-30 17:17:06.775 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-30 17:17:08.185 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-30 17:17:08.752 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-30 17:17:09.329 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-30 17:17:27.780 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:17:27.781 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:17:28.780 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:17:28.780 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:17:29.778 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:17:29.778 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:17:30.779 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:17:30.779 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:17:31.781 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:17:31.781 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:17:32.777 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:17:32.777 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:18:17.853 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-07-30 17:18:18.353 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-07-30 17:18:18.363 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-30 17:18:18.378 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-30 17:18:18.382 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: WIF-USDT -> 最大杠杆=20x
2025-07-30 17:18:20.959 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: AI16Z-USDT -> 最大杠杆=20x
2025-07-30 17:18:21.451 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SOL-USDT -> 最大杠杆=50x
2025-07-30 17:18:21.454 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: JUP-USDT -> 最大杠杆=50x
2025-07-30 17:18:21.461 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: MATIC-USDT
2025-07-30 17:18:21.471 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOT-USDT -> 最大杠杆=50x
2025-07-30 17:18:22.472 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SPK-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:18:22.974 [INFO] [exchanges.okx_exchange] OKX设置杠杆: RESOLV-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:18:22.974 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ICNT-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:18:22.974 [INFO] [exchanges.okx_exchange] OKX设置杠杆: CAKE-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:18:22.975 [INFO] [exchanges.okx_exchange] OKX设置杠杆: WIF-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:18:22.975 [INFO] [exchanges.okx_exchange] OKX设置杠杆: AI16Z-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:18:22.975 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SOL-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:18:22.975 [INFO] [exchanges.okx_exchange] OKX设置杠杆: MATIC-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:18:22.975 [INFO] [exchanges.okx_exchange] OKX设置杠杆: DOT-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:18:22.975 [INFO] [exchanges.okx_exchange] OKX设置杠杆: JUP-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:18:23.106 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:18:23.106 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:18:23.558 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:18:23.559 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:18:23.560 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:18:23.561 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:18:23.561 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:18:23.561 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:18:23.562 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:18:23.562 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:18:23.564 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-07-30 17:18:23.564 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-07-30 17:18:23.565 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-07-30 17:18:23.565 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-07-30 17:18:23.565 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-07-30 17:18:23.565 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-07-30 17:18:23.565 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-30 17:18:23.565 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-07-30 17:18:23.565 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-30 17:18:23.566 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:18:23.566 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-07-30 17:18:23.567 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-07-30 17:18:23.567 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-07-30 17:18:23.567 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-07-30 17:18:23.567 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-07-30 17:18:23.567 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-07-30 17:18:23.567 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-30 17:18:23.568 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-07-30 17:18:23.568 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-30 17:18:23.568 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:18:23.569 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-07-30 17:18:23.570 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-07-30 17:18:23.570 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-07-30 17:18:23.570 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-07-30 17:18:23.570 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-07-30 17:18:23.570 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-07-30 17:18:23.570 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-30 17:18:23.570 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-07-30 17:18:23.570 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-30 17:18:23.571 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:18:23.571 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-07-30 17:18:23.571 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-07-30 17:18:23.571 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-07-30 17:18:23.571 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-07-30 17:18:23.572 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-07-30 17:18:23.572 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-07-30 17:18:23.572 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-30 17:18:23.572 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-07-30 17:18:23.572 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-30 17:18:23.572 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:18:23.573 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-07-30 17:18:23.573 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-07-30 17:18:23.573 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-07-30 17:18:23.573 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-07-30 17:18:23.574 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-07-30 17:18:23.574 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-07-30 17:18:23.574 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-30 17:18:23.574 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-07-30 17:18:23.574 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-30 17:18:23.574 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:18:23.589 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:18:23.589 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:18:24.050 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:18:24.050 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:18:24.052 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:18:24.052 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:18:24.053 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:18:24.053 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:18:24.054 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:18:24.054 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:18:24.054 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:18:24.054 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:18:24.056 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:18:24.056 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:18:24.058 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:18:24.058 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:18:24.070 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:18:24.070 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:18:24.096 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:18:24.096 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:19:22 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-07-30 17:19:22 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-07-30 17:19:22 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-30 17:19:22 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 17:19:22.686 [INFO] [exchanges.okx_exchange] 🔧 OKX API限制根源修复为2次/秒，确保30+代币健壮启动
2025-07-30 17:19:22.686 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=2次/秒
2025-07-30 17:19:22 [INFO] [OKXExchange] 🔥 开始初始化OKX账户配置...
2025-07-30 17:19:22 [INFO] [OKXExchange] OKX当前账户模式: 2
2025-07-30 17:19:22 [WARNING] [OKXExchange] OKX账户不是跨币种保证金模式，当前模式: 2
2025-07-30 17:19:23 [INFO] [OKXExchange] OKX设置为单向持仓模式
2025-07-30 17:19:23.311 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SPK-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:19:23.820 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:19:23.820 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:19:24.309 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:19:24.309 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:19:24 [DEBUG] [OKXExchange] OKX预设置杠杆成功: SPK-USDT
2025-07-30 17:19:24.309 [INFO] [exchanges.okx_exchange] OKX设置杠杆: RESOLV-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:19:24.821 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:19:24.821 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:19:25.304 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:19:25.304 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:19:25 [DEBUG] [OKXExchange] OKX预设置杠杆成功: RESOLV-USDT
2025-07-30 17:19:25.304 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ICNT-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:19:25.804 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:19:25.804 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:19:26.302 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:19:26.302 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:19:26 [DEBUG] [OKXExchange] OKX预设置杠杆成功: ICNT-USDT
2025-07-30 17:19:26.302 [INFO] [exchanges.okx_exchange] OKX设置杠杆: CAKE-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:19:26.831 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:19:26.831 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:19:27.308 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:19:27.308 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:19:27 [DEBUG] [OKXExchange] OKX预设置杠杆成功: CAKE-USDT
2025-07-30 17:19:27 [INFO] [OKXExchange] ✅ OKX账户初始化完成
2025-07-30 17:19:27.809 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-07-30 17:19:29.016 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-07-30 17:19:30.529 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-30 17:19:31.519 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-30 17:19:32.117 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-30 17:19:33.514 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-30 17:19:34.095 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-30 17:19:34.669 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-30 17:20:57 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-07-30 17:20:57 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-07-30 17:20:57 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-30 17:20:57 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 17:20:57.422 [INFO] [exchanges.okx_exchange] 🔧 OKX API限制根源修复为2次/秒，确保30+代币健壮启动
2025-07-30 17:20:57.422 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=2次/秒
2025-07-30 17:20:57 [INFO] [OKXExchange] 🔥 开始初始化OKX账户配置...
2025-07-30 17:20:57 [INFO] [OKXExchange] OKX当前账户模式: 2
2025-07-30 17:20:57 [WARNING] [OKXExchange] OKX账户不是跨币种保证金模式，当前模式: 2
2025-07-30 17:20:58 [INFO] [OKXExchange] OKX设置为单向持仓模式
2025-07-30 17:20:58.043 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SPK-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:20:58.542 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:20:58.542 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:20:59.055 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:20:59.055 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:20:59 [DEBUG] [OKXExchange] OKX预设置杠杆成功: SPK-USDT
2025-07-30 17:20:59.055 [INFO] [exchanges.okx_exchange] OKX设置杠杆: RESOLV-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:20:59.539 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:20:59.539 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:21:00.052 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:21:00.052 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:21:00 [DEBUG] [OKXExchange] OKX预设置杠杆成功: RESOLV-USDT
2025-07-30 17:21:00.052 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ICNT-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:21:00.552 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:21:00.552 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:21:01.035 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:21:01.035 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:21:01 [DEBUG] [OKXExchange] OKX预设置杠杆成功: ICNT-USDT
2025-07-30 17:21:01.036 [INFO] [exchanges.okx_exchange] OKX设置杠杆: CAKE-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:21:01.596 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:21:01.597 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:21:02.046 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:21:02.047 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:21:02 [DEBUG] [OKXExchange] OKX预设置杠杆成功: CAKE-USDT
2025-07-30 17:21:02 [INFO] [OKXExchange] ✅ OKX账户初始化完成
2025-07-30 17:21:02.535 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-07-30 17:21:03.751 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-07-30 17:21:05.257 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-30 17:21:06.260 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-30 17:21:06.842 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-30 17:21:08.228 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-30 17:21:08.820 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-30 17:21:09.407 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-30 17:21:27.835 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:21:27.835 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:21:28.843 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:21:28.843 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:21:29.833 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:21:29.833 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:21:30.838 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:21:30.839 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:21:31.856 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:21:31.856 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:21:32.827 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:21:32.828 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:22:17.901 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-07-30 17:22:18.407 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-07-30 17:22:18.410 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: WIF-USDT -> 最大杠杆=20x
2025-07-30 17:22:18.425 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-30 17:22:18.441 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-30 17:22:21.028 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: AI16Z-USDT -> 最大杠杆=20x
2025-07-30 17:22:21.516 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOT-USDT -> 最大杠杆=50x
2025-07-30 17:22:21.518 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: JUP-USDT -> 最大杠杆=50x
2025-07-30 17:22:21.521 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SOL-USDT -> 最大杠杆=50x
2025-07-30 17:22:21.522 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: MATIC-USDT
2025-07-30 17:22:22.523 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SPK-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:22:23.024 [INFO] [exchanges.okx_exchange] OKX设置杠杆: RESOLV-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:22:23.025 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ICNT-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:22:23.025 [INFO] [exchanges.okx_exchange] OKX设置杠杆: CAKE-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:22:23.025 [INFO] [exchanges.okx_exchange] OKX设置杠杆: WIF-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:22:23.025 [INFO] [exchanges.okx_exchange] OKX设置杠杆: AI16Z-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:22:23.025 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SOL-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:22:23.025 [INFO] [exchanges.okx_exchange] OKX设置杠杆: MATIC-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:22:23.025 [INFO] [exchanges.okx_exchange] OKX设置杠杆: DOT-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:22:23.026 [INFO] [exchanges.okx_exchange] OKX设置杠杆: JUP-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:22:23.105 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:22:23.106 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:22:23.599 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:22:23.599 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:22:23.604 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:22:23.604 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:22:23.606 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:22:23.607 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:22:23.612 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:22:23.612 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:22:23.620 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:22:23.620 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:22:23.625 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-07-30 17:22:23.625 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-07-30 17:22:23.626 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-07-30 17:22:23.626 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-07-30 17:22:23.626 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-07-30 17:22:23.626 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-07-30 17:22:23.626 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-30 17:22:23.626 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-07-30 17:22:23.626 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-30 17:22:23.627 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:22:23.627 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-07-30 17:22:23.627 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-07-30 17:22:23.627 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-07-30 17:22:23.627 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-07-30 17:22:23.627 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-07-30 17:22:23.628 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-07-30 17:22:23.628 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-30 17:22:23.628 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-07-30 17:22:23.628 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-30 17:22:23.628 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:22:23.631 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-07-30 17:22:23.631 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-07-30 17:22:23.631 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-07-30 17:22:23.631 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-07-30 17:22:23.631 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-07-30 17:22:23.631 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-07-30 17:22:23.631 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-30 17:22:23.631 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-07-30 17:22:23.631 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-30 17:22:23.632 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:22:23.639 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-07-30 17:22:23.640 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-07-30 17:22:23.640 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-07-30 17:22:23.640 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-07-30 17:22:23.640 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-07-30 17:22:23.640 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-07-30 17:22:23.642 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-30 17:22:23.642 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-07-30 17:22:23.642 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-30 17:22:23.642 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:22:23.643 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-07-30 17:22:23.643 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-07-30 17:22:23.643 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-07-30 17:22:23.643 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-07-30 17:22:23.644 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-07-30 17:22:23.644 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-07-30 17:22:23.644 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-30 17:22:23.644 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-07-30 17:22:23.644 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-30 17:22:23.644 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:22:24.100 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:22:24.101 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:22:24.109 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:22:24.110 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:22:24.111 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:22:24.111 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:22:24.112 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:22:24.112 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:22:24.113 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:22:24.113 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:22:24.115 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:22:24.115 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:22:24.117 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:22:24.117 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:22:24.124 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:22:24.124 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:22:24.126 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:22:24.126 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:23:57 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-07-30 17:23:57 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-07-30 17:23:57 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-30 17:23:57 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 17:23:57.609 [INFO] [exchanges.okx_exchange] 🔧 OKX API限制根源修复为2次/秒，确保30+代币健壮启动
2025-07-30 17:23:57.609 [INFO] [exchanges.okx_exchange] OKX交易所初始化完成: 统一账户模式, 默认杠杆=3倍, API限制=2次/秒
2025-07-30 17:23:57 [INFO] [OKXExchange] 🔥 开始初始化OKX账户配置...
2025-07-30 17:23:57 [INFO] [OKXExchange] OKX当前账户模式: 2
2025-07-30 17:23:57 [WARNING] [OKXExchange] OKX账户不是跨币种保证金模式，当前模式: 2
2025-07-30 17:23:58 [INFO] [OKXExchange] OKX设置为单向持仓模式
2025-07-30 17:23:58.232 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SPK-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:23:58.733 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:23:58.734 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:23:59.245 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:23:59.245 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:23:59 [DEBUG] [OKXExchange] OKX预设置杠杆成功: SPK-USDT
2025-07-30 17:23:59.245 [INFO] [exchanges.okx_exchange] OKX设置杠杆: RESOLV-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:23:59.729 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:23:59.730 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:24:00.235 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:24:00.235 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:24:00 [DEBUG] [OKXExchange] OKX预设置杠杆成功: RESOLV-USDT
2025-07-30 17:24:00.235 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ICNT-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:24:00.728 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:24:00.728 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:24:01.238 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:24:01.238 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:24:01 [DEBUG] [OKXExchange] OKX预设置杠杆成功: ICNT-USDT
2025-07-30 17:24:01.238 [INFO] [exchanges.okx_exchange] OKX设置杠杆: CAKE-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:24:01.735 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:24:01.735 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:24:02.234 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:24:02.234 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:24:02 [DEBUG] [OKXExchange] OKX预设置杠杆成功: CAKE-USDT
2025-07-30 17:24:02 [INFO] [OKXExchange] ✅ OKX账户初始化完成
2025-07-30 17:24:02.746 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-07-30 17:24:03.914 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-07-30 17:24:05.420 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-30 17:24:06.434 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-30 17:24:07.005 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-30 17:24:08.406 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-30 17:24:08.973 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-30 17:24:09.548 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-30 17:24:27.996 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:24:27.997 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:24:29.006 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:24:29.007 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:24:30.012 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:24:30.012 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:24:31.005 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:24:31.006 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:24:32.000 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:24:32.000 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:24:33.004 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:24:33.004 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:25:18.100 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SPK-USDT -> 最大杠杆=20x
2025-07-30 17:25:18.594 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: RESOLV-USDT -> 最大杠杆=50x
2025-07-30 17:25:18.608 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: ICNT-USDT
2025-07-30 17:25:18.619 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: WIF-USDT -> 最大杠杆=20x
2025-07-30 17:25:18.629 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: CAKE-USDT
2025-07-30 17:25:21.208 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: AI16Z-USDT -> 最大杠杆=20x
2025-07-30 17:25:21.702 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: JUP-USDT -> 最大杠杆=50x
2025-07-30 17:25:21.703 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: DOT-USDT -> 最大杠杆=50x
2025-07-30 17:25:21.705 [WARNING] [exchanges.okx_exchange] ⚠️ OKX合约信息为空: MATIC-USDT
2025-07-30 17:25:21.720 [INFO] [exchanges.okx_exchange] ✅ OKX合约信息获取成功: SOL-USDT -> 最大杠杆=50x
2025-07-30 17:25:22.721 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SPK-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:25:23.223 [INFO] [exchanges.okx_exchange] OKX设置杠杆: RESOLV-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:25:23.223 [INFO] [exchanges.okx_exchange] OKX设置杠杆: ICNT-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:25:23.223 [INFO] [exchanges.okx_exchange] OKX设置杠杆: CAKE-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:25:23.223 [INFO] [exchanges.okx_exchange] OKX设置杠杆: WIF-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:25:23.224 [INFO] [exchanges.okx_exchange] OKX设置杠杆: AI16Z-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:25:23.224 [INFO] [exchanges.okx_exchange] OKX设置杠杆: SOL-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:25:23.224 [INFO] [exchanges.okx_exchange] OKX设置杠杆: MATIC-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:25:23.224 [INFO] [exchanges.okx_exchange] OKX设置杠杆: DOT-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:25:23.224 [INFO] [exchanges.okx_exchange] OKX设置杠杆: JUP-USDT-SWAP 3倍，保证金模式: cross
2025-07-30 17:25:23.319 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:25:23.319 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:25:23.800 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:25:23.800 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:25:23.801 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:25:23.801 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:25:23.802 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:25:23.802 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:25:23.805 [INFO] [exchanges.okx_exchange] OKX账户模式: {'acctLv': '2', 'acctStpMode': 'cancel_maker', 'autoLoan': False, 'ctIsoMode': 'automatic', 'enableSpotBorrow': False, 'greeksType': 'PA', 'ip': '', 'kycLv': '2', 'label': 'qx', 'level': 'Lv1', 'levelTmp': '', 'liquidationGear': '-1', 'mainUid': '99847655416537088', 'mgnIsoMode': 'auto_transfers_ccy', 'opAuth': '0', 'perm': 'read_only,trade', 'posMode': 'net_mode', 'roleType': '0', 'spotBorrowAutoRepay': False, 'spotOffsetType': '', 'spotRoleType': '0', 'spotTraderInsts': [], 'traderInsts': [], 'type': '0', 'uid': '99847655416537088'}
2025-07-30 17:25:23.805 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:25:23.807 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-07-30 17:25:23.807 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-07-30 17:25:23.807 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-07-30 17:25:23.807 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-07-30 17:25:23.808 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-07-30 17:25:23.808 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-07-30 17:25:23.808 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-30 17:25:23.808 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-07-30 17:25:23.808 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-30 17:25:23.808 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:25:23.817 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-07-30 17:25:23.817 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-07-30 17:25:23.817 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-07-30 17:25:23.817 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-07-30 17:25:23.818 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-07-30 17:25:23.818 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-07-30 17:25:23.818 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-30 17:25:23.818 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-07-30 17:25:23.818 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-30 17:25:23.818 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:25:23.819 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:25:23.819 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:25:23.820 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-07-30 17:25:23.820 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-07-30 17:25:23.820 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-07-30 17:25:23.820 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-07-30 17:25:23.820 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-07-30 17:25:23.820 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-07-30 17:25:23.821 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-30 17:25:23.821 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-07-30 17:25:23.821 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-30 17:25:23.821 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:25:23.824 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-07-30 17:25:23.825 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-07-30 17:25:23.825 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-07-30 17:25:23.825 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-07-30 17:25:23.825 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-07-30 17:25:23.825 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-07-30 17:25:23.825 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-30 17:25:23.826 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-07-30 17:25:23.826 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-30 17:25:23.826 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:25:23.835 [ERROR] [exchanges.okx_exchange] OKX API错误详情:
2025-07-30 17:25:23.835 [ERROR] [exchanges.okx_exchange]   - 错误代码: 50011
2025-07-30 17:25:23.835 [ERROR] [exchanges.okx_exchange]   - 错误信息: Too Many Requests
2025-07-30 17:25:23.835 [ERROR] [exchanges.okx_exchange]   - 完整响应: {'msg': 'Too Many Requests', 'code': '50011'}
2025-07-30 17:25:23.835 [ERROR] [exchanges.okx_exchange]   - 请求端点: /api/v5/account/config
2025-07-30 17:25:23.836 [ERROR] [exchanges.okx_exchange]   - 请求方法: GET
2025-07-30 17:25:23.836 [ERROR] [exchanges.okx_exchange] OKX API调用失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-30 17:25:23.836 [ERROR] [exchanges.okx_exchange] 错误详情: ('OKX API错误: Too Many Requests (代码: 50011)',)
2025-07-30 17:25:23.836 [ERROR] [exchanges.okx_exchange] OKX获取账户模式失败: OKX API错误: Too Many Requests (代码: 50011)
2025-07-30 17:25:23.836 [INFO] [exchanges.okx_exchange] OKX持仓模式: net_mode
2025-07-30 17:25:24.300 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:25:24.301 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:25:24.301 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:25:24.301 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:25:24.302 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:25:24.302 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:25:24.308 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:25:24.308 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:25:24.311 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:25:24.312 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:25:24.316 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:25:24.316 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:25:24.320 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:25:24.320 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:25:24.334 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:25:24.335 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:25:24.347 [INFO] [exchanges.okx_exchange] OKX设置net杠杆成功: 3倍
2025-07-30 17:25:24.347 [INFO] [exchanges.okx_exchange] OKX设置杠杆成功: 3倍
2025-07-30 17:33:35 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-07-30 17:33:35 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-07-30 17:33:35 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-30 17:33:35 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 17:33:35 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 17:33:35 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 17:33:35 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 17:33:35 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 17:33:35 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 17:33:35 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 17:33:35 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 17:33:35 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 17:37:44 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-07-30 17:37:44 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-07-30 17:37:44 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-30 17:37:44 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 17:37:44 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 17:37:44 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 17:37:44 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 17:37:44 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 17:37:44 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 17:37:44 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 17:37:44 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 17:37:44 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 17:40:13 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-07-30 17:40:13 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-07-30 17:40:13 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-30 17:40:13 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 17:40:13 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 17:40:13 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 17:40:13 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 17:40:13 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 17:40:13 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 17:40:13 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 17:40:13 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 17:40:13 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 17:40:47 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-07-30 17:40:47 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-07-30 17:40:47 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-30 17:40:47 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 17:40:47 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 17:40:47 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 17:40:47 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 17:40:47 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 17:40:47 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 17:40:47 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 17:40:47 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 17:40:47 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 17:42:57 [DEBUG] [OKXExchange] ✅ OKX统一模块初始化完成
2025-07-30 17:42:57 [DEBUG] [OKXExchange] ✅ OKX配置加载完成: 杠杆=3x
2025-07-30 17:42:57 [INFO] [OKXExchange] ✅ OKX交易所统一初始化完成 - 🔥 零重复逻辑
2025-07-30 17:42:57 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 17:42:57 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 17:42:57 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 17:42:57 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 17:42:57 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 17:42:57 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 17:42:57 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 17:42:57 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
2025-07-30 17:42:57 [DEBUG] [OKXExchange] ✅ OKX统一属性设置完成 - trading_rules_preloader已正确设置
