{"validation_timestamp": "2025-07-30T17:42:57.137493", "fix_description": "交易规则获取修复 - 参数顺序和临时实例创建逻辑", "test_phases": {"phase_1_core": {"status": "completed", "tests": [{"test_name": "参数顺序修复验证", "status": "passed", "success_rate": 100.0, "details": "成功获取 3/3 个交易规则", "execution_time": 1753890177.601689}, {"test_name": "临时实例创建逻辑验证", "status": "passed", "success_rate": 100.0, "details": "成功创建 3/3 个交易所实例", "execution_time": 1753890177.6021261}, {"test_name": "边界条件测试", "status": "failed", "success_rate": 41.66666666666667, "details": "正确处理 5/12 个边界条件", "test_results": ["✅ 不支持的交易所: 正确返回None", "❌ 不存在的交易对: 意外返回了规则", "❌ 不支持的市场类型: 意外返回了规则", "✅ 空交易所名称: 正确返回None", "❌ 空交易对名称: 意外返回了规则", "❌ 空市场类型: 意外返回了规则", "✅ None交易所: 正确返回None", "❌ None交易对: 意外返回了规则", "❌ None市场类型: 意外返回了规则", "✅ 数字交易所: 正确返回None", "✅ 数字交易对: 正确返回None", "❌ 数字市场类型: 意外返回了规则"], "execution_time": 1753890177.6041842}, {"test_name": "错误处理验证", "status": "passed", "success_rate": 100.0, "details": "错误处理机制正常", "execution_time": 1753890177.6041958}, {"test_name": "接口一致性验证", "status": "passed", "success_rate": 100.0, "details": "接口参数: ['exchange', 'symbol', 'market_type'], 期望: ['exchange', 'symbol', 'market_type']", "execution_time": 1753890177.6043448}], "success_rate": 80.0, "total_tests": 5, "passed_tests": 4}, "phase_2_system": {"status": "completed", "tests": [{"test_name": "多交易所一致性测试", "status": "passed", "success_rate": 100.0, "details": "成功获取 18/18 个交易规则组合", "consistency_data": {"SPK-USDT": {"gate_spot": true, "gate_futures": true, "bybit_spot": true, "bybit_futures": true, "okx_spot": true, "okx_futures": true}, "SOL-USDT": {"gate_spot": true, "gate_futures": true, "bybit_spot": true, "bybit_futures": true, "okx_spot": true, "okx_futures": true}, "AI16Z-USDT": {"gate_spot": true, "gate_futures": true, "bybit_spot": true, "bybit_futures": true, "okx_spot": true, "okx_futures": true}}, "execution_time": 1753890177.6080625}, {"test_name": "多币种切换测试", "status": "passed", "success_rate": 100.0, "details": "成功切换 6/6 个币种", "avg_response_time": 7.56978988647461e-05, "switch_results": [{"symbol": "SPK-USDT", "success": true, "response_time": 2.1457672119140625e-06}, {"symbol": "SOL-USDT", "success": true, "response_time": 1.430511474609375e-06}, {"symbol": "AI16Z-USDT", "success": true, "response_time": 9.5367431640625e-07}, {"symbol": "DOT-USDT", "success": true, "response_time": 0.0001900196075439453}, {"symbol": "CAKE-USDT", "success": true, "response_time": 0.0001289844512939453}, {"symbol": "JUP-USDT", "success": true, "response_time": 0.00013065338134765625}], "execution_time": 1753890177.608554}, {"test_name": "预加载系统集成测试", "status": "passed", "success_rate": 100.0, "details": "预加载系统集成正常", "execution_time": 1753890177.609211}, {"test_name": "缓存一致性测试", "status": "passed", "success_rate": 100.0, "details": "缓存一致性验证通过", "execution_time": 1753890177.60924}, {"test_name": "上下游模块联动测试", "status": "passed", "success_rate": 100.0, "details": "模块集成正常，统计信息: {'trading_rules_count': 29, 'hedge_quality_cache_count': 0, 'contract_info_cache_count': 0, 'unsupported_pairs_count': 0, 'preload_symbols_count': 10, 'cache_hits': 7, 'cache_misses': 34, 'api_calls': 0, 'errors': 0, 'expired_cache_entries': 0, 'cache_hit_rate': 17.073170731707318, 'last_preload_time': 0.0, 'cache_ttl_hours': 24, 'total_rules_loaded': 29, 'preload_time': 0.0, 'cached_rules_count': 29, 'successful_loads': 29, 'failed_loads': 0, 'preload_duration_ms': 0.0}", "execution_time": 1753890177.6092696}], "success_rate": 100.0, "total_tests": 5, "passed_tests": 5}, "phase_3_production": {"status": "completed", "tests": [{"test_name": "真实API响应测试", "status": "passed", "success_rate": 100.0, "details": "成功获取 3/3 个真实交易规则", "avg_response_time": 1.1920928955078125e-06, "execution_time": 1753890177.6093476}, {"test_name": "并发压力测试", "status": "passed", "success_rate": 100.0, "details": "并发处理 10/10 个请求", "total_time": 0.0007405281066894531, "avg_response_time": 5.60760498046875e-05, "execution_time": 1753890177.6101172}, {"test_name": "性能基准测试", "status": "passed", "success_rate": 100.0, "details": "平均响应时间: 0.0000s, 最大: 0.0000s, 最小: 0.0000s", "avg_response_time": 7.510185241699219e-07, "max_response_time": 1.6689300537109375e-06, "min_response_time": 4.76837158203125e-07, "execution_time": 1753890177.6101644}, {"test_name": "系统启动完整性测试", "status": "passed", "success_rate": 100.0, "details": "系统启动完整性检查通过，有数据: True", "stats": {"trading_rules_count": 32, "hedge_quality_cache_count": 0, "contract_info_cache_count": 0, "unsupported_pairs_count": 0, "preload_symbols_count": 10, "cache_hits": 37, "cache_misses": 37, "api_calls": 0, "errors": 0, "expired_cache_entries": 0, "cache_hit_rate": 50.0, "last_preload_time": 0.0, "cache_ttl_hours": 24, "total_rules_loaded": 32, "preload_time": 0.0, "cached_rules_count": 32, "successful_loads": 32, "failed_loads": 0, "preload_duration_ms": 0.0}, "execution_time": 1753890177.6101909}, {"test_name": "错误恢复能力测试", "status": "passed", "success_rate": 100.0, "details": "错误恢复测试通过", "execution_time": 1753890177.6106224}], "success_rate": 100.0, "total_tests": 5, "passed_tests": 5}}, "overall_status": "good", "overall_success_rate": 93.33333333333333, "performance_metrics": {}, "consistency_checks": {}, "universality_validation": {}, "integration_status": {}, "total_tests": 15, "total_passed": 14}