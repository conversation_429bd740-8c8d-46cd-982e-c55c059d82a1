# TotalBalance

User's balance in all accounts
## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**total** | [**AccountBalance**](AccountBalance.md) |  | [optional] 
**details** | [**dict(str, AccountBalance)**](AccountBalance.md) | Total balances in different accounts  - cross_margin: cross margin account - spot: spot account - finance: finance account - margin: margin account - quant: quant account - futures: futures account - delivery: delivery account - warrant: warrant account - cbbc: cbbc account | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


