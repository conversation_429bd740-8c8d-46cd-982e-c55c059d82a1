# Trade

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **str** | Trade ID | [optional] 
**create_time** | **str** | Trading time | [optional] 
**create_time_ms** | **str** | Trading time, with millisecond precision | [optional] 
**currency_pair** | **str** | Currency pair | [optional] 
**side** | **str** | Order side | [optional] 
**role** | **str** | Trade role. No value in public endpoints | [optional] 
**amount** | **str** | Trade amount | [optional] 
**price** | **str** | Order price | [optional] 
**order_id** | **str** | Related order ID. No value in public endpoints | [optional] 
**fee** | **str** | Fee deducted. No value in public endpoints | [optional] 
**fee_currency** | **str** | Fee currency unit. No value in public endpoints | [optional] 
**point_fee** | **str** | Points used to deduct fee. No value in public endpoints | [optional] 
**gt_fee** | **str** | GT used to deduct fee. No value in public endpoints | [optional] 
**amend_text** | **str** | The custom data that the user remarked when amending the order | [optional] 
**sequence_id** | **str** | Represents a unique and consecutive trade ID within a single market. It is used to track and identify trades in the specific market | [optional] 
**text** | **str** | User defined information. No value in public endpoints | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


