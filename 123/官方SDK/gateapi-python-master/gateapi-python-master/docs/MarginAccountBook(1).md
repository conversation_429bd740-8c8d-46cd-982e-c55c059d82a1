# MarginAccountBook

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **str** | Balance change record ID | [optional] 
**time** | **str** | Balance changed timestamp | [optional] 
**time_ms** | **int** | The timestamp of the change (in milliseconds) | [optional] 
**currency** | **str** | Currency changed | [optional] 
**currency_pair** | **str** | Account currency pair | [optional] 
**change** | **str** | Amount changed. Positive value means transferring in, while negative out | [optional] 
**balance** | **str** | Balance after change | [optional] 
**type** | **str** | Account book type.  Please refer to [account book type](#accountbook-type) for more detail | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


