# PositionClose

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**time** | **float** | Position close time | [optional] [readonly] 
**contract** | **str** | Futures contract | [optional] [readonly] 
**side** | **str** | Position side, long or short | [optional] [readonly] 
**pnl** | **str** | PNL | [optional] [readonly] 
**pnl_pnl** | **str** | PNL - Position P/L | [optional] [readonly] 
**pnl_fund** | **str** | PNL - Funding Fees | [optional] [readonly] 
**pnl_fee** | **str** | PNL - Transaction Fees | [optional] [readonly] 
**text** | **str** | Text of close order | [optional] [readonly] 
**max_size** | **str** | Max Trade Size | [optional] [readonly] 
**accum_size** | **str** | Cumulative closed position volume | [optional] [readonly] 
**first_open_time** | **int** | First Open Time | [optional] [readonly] 
**long_price** | **str** | When &#39;side&#39; is &#39;long,&#39; it indicates the opening average price; when &#39;side&#39; is &#39;short,&#39; it indicates the closing average price. | [optional] [readonly] 
**short_price** | **str** | When &#39;side&#39; is &#39;long,&#39; it indicates the opening average price; when &#39;side&#39; is &#39;short,&#39; it indicates the closing average price | [optional] [readonly] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


