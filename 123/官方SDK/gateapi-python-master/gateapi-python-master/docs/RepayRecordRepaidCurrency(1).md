# RepayRecordRepaidCurrency

## Properties
Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**currency** | **str** | Repayment currency | [optional] 
**index_price** | **str** | Currency Index Price | [optional] 
**repaid_amount** | **str** | Repayment amount | [optional] 
**repaid_principal** | **str** | Principal | [optional] 
**repaid_interest** | **str** | Interest | [optional] 
**repaid_amount_usdt** | **str** | Value of the repayment amount in USDT | [optional] 

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


